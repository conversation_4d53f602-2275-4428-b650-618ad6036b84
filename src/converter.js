/**
 * 修复后的节点转换器
 */

const { buildClashConfig } = require('./templates/clash-config.js');

class NodeConverter {
  // 解析VMess链接
  parseVmess(config) {
    if (config.startsWith('vmess://')) {
      try {
        const base64Data = config.substring(8); // 移除 'vmess://' 前缀
        const jsonStr = atob(base64Data); // Base64解码
        const vmessConfig = JSON.parse(jsonStr);

        // 基础配置
        const node = {
          type: 'vmess',
          name: vmessConfig.ps || 'VMess节点',
          server: vmessConfig.add,
          port: parseInt(vmessConfig.port),
          uuid: vmessConfig.id,
          alterId: parseInt(vmessConfig.aid) || 0,
          cipher: vmessConfig.scy || 'auto',
          network: vmessConfig.net || 'tcp'
        };

        // 验证必要字段
        if (!node.server || !node.port || !node.uuid) {
          throw new Error('VMess链接缺少必要参数');
        }

        // TLS配置
        if (vmessConfig.tls === 'tls') {
          node.tls = true;
          node.sni = vmessConfig.sni || vmessConfig.host || node.server;
        }

        // 传输协议配置
        switch (node.network) {
          case 'tcp':
            node.headerType = vmessConfig.type || 'none';
            if (vmessConfig.host) {
              node.httpHeaders = { Host: vmessConfig.host };
            }
            break;
          case 'ws':
            node.path = vmessConfig.path || '/';
            node.host = vmessConfig.host || '';
            break;
          case 'h2':
            node.path = vmessConfig.path || '/';
            node.host = vmessConfig.host || '';
            break;
          case 'grpc':
            node.serviceName = vmessConfig.path || 'GunService';
            node.mode = vmessConfig.mode || 'gun';
            break;
        }

        return node;
      } catch (error) {
        throw new Error(`VMess链接解析失败: ${error.message}`);
      }
    }
    throw new Error('无效的VMess链接');
  }

  // 解析VLess链接
  parseVless(config) {
    if (config.startsWith('vless://')) {
      try {
        const url = new URL(config);
        const params = url.searchParams;
        
        // 基础配置
        const node = {
          type: 'vless',
          name: decodeURIComponent(url.hash.substring(1)) || 'VLess节点',
          server: url.hostname,
          port: parseInt(url.port),
          uuid: url.username,
          encryption: params.get('encryption') || 'none',
          network: params.get('type') || 'tcp'
        };

        // 验证必要字段
        if (!node.server || !node.port || !node.uuid) {
          throw new Error('VLess链接缺少必要参数');
        }

        // 安全传输配置
        const security = params.get('security');
        if (security === 'tls') {
          node.tls = true;
          node.sni = params.get('sni') || url.hostname;
          node.alpn = params.get('alpn');
          node.fingerprint = params.get('fp');
        } else if (security === 'reality') {
          node.reality = true;
          node.sni = params.get('sni') || url.hostname;
          node.fingerprint = params.get('fp') || 'chrome';
          node.publicKey = params.get('pbk');
          node.shortId = params.get('sid');
          node.spiderX = params.get('spx');
        }

        // Flow控制 (XTLS)
        const flow = params.get('flow');
        if (flow) {
          node.flow = flow;
        }

        // 传输协议配置
        switch (node.network) {
          case 'tcp':
            node.headerType = params.get('headerType') || 'none';
            break;
          case 'ws':
            node.path = params.get('path') || '/';
            node.host = params.get('host') || '';
            break;
          case 'grpc':
            node.serviceName = params.get('serviceName') || 'GunService';
            node.mode = params.get('mode') || 'gun';
            break;
          case 'h2':
            node.path = params.get('path') || '/';
            node.host = params.get('host') || '';
            break;
        }

        return node;
      } catch (error) {
        throw new Error(`VLess链接解析失败: ${error.message}`);
      }
    }
    throw new Error('无效的VLess链接');
  }

  // 解析节点
  parseNode(config, type) {
    switch (type) {
      case 'vmess':
        return this.parseVmess(config);
      case 'vless':
        return this.parseVless(config);
      default:
        throw new Error(`不支持的节点类型: ${type}`);
    }
  }

  // 节点转Clash代理配置
  nodeToClashProxy(node) {
    if (node.type === 'vmess') {
      const vmessProxy = {
        name: node.name,
        type: 'vmess',
        server: node.server,
        port: node.port,
        uuid: node.uuid,
        alterId: node.alterId || 0,
        cipher: node.cipher || 'auto',
        tfo: false,
        'skip-cert-verify': false
      };

      // TLS配置
      if (node.tls) {
        vmessProxy.tls = true;
        if (node.sni) {
          vmessProxy.servername = node.sni;
        }
      }

      // 网络配置
      vmessProxy.network = node.network || 'tcp';

      // 添加网络配置选项
      if (node.network === 'ws') {
        vmessProxy['ws-opts'] = {
          path: node.path || '/',
          headers: node.host ? { Host: node.host } : {}
        };
      } else if (node.network === 'h2') {
        vmessProxy['h2-opts'] = {
          host: [node.host || node.server],
          path: node.path || '/'
        };
      } else if (node.network === 'grpc') {
        vmessProxy['grpc-opts'] = {
          'grpc-service-name': node.serviceName || 'GunService'
        };
      } else if (node.network === 'tcp' && node.headerType === 'http') {
        vmessProxy['http-opts'] = {
          method: 'GET',
          path: ['/'],
          headers: node.httpHeaders || {}
        };
      }

      return vmessProxy;
    } else if (node.type === 'vless') {
      const vlessProxy = {
        name: node.name,
        type: 'vless',
        server: node.server,
        port: node.port,
        uuid: node.uuid,
        'client-fingerprint': 'chrome',
        tfo: false,
        'skip-cert-verify': false
      };

      // 设置TLS和安全传输
      if (node.reality) {
        vlessProxy.tls = true;
        // REALITY配置
        if (node.publicKey || node.shortId) {
          vlessProxy['reality-opts'] = {};
          if (node.publicKey) vlessProxy['reality-opts']['public-key'] = node.publicKey;
          if (node.shortId) vlessProxy['reality-opts']['short-id'] = node.shortId;
        }
      } else if (node.tls) {
        vlessProxy.tls = true;
      }

      // 添加服务器名称
      if (node.sni) {
        vlessProxy.servername = node.sni;
      }

      // 添加网络类型
      vlessProxy.network = node.network || 'tcp';

      // 添加Flow控制 (XTLS)
      if (node.flow) {
        vlessProxy.flow = node.flow;
      }

      // 添加客户端指纹
      if (node.fingerprint) {
        vlessProxy['client-fingerprint'] = node.fingerprint;
      }

      // 添加网络配置选项
      if (node.network === 'ws') {
        vlessProxy['ws-opts'] = {
          path: node.path || '/',
          headers: node.host ? { Host: node.host } : {}
        };
      } else if (node.network === 'grpc') {
        vlessProxy['grpc-opts'] = {
          'grpc-service-name': node.serviceName || 'GunService'
        };
      } else if (node.network === 'h2') {
        vlessProxy['h2-opts'] = {
          host: [node.host || node.server],
          path: node.path || '/'
        };
      }

      return vlessProxy;
    }
    return null;
  }

  // 转换为Clash配置
  toClashConfig(nodes, options = {}) {
    const proxies = nodes.map(node => this.nodeToClashProxy(node)).filter(Boolean);
    return buildClashConfig(proxies, options);
  }

  // 转换为V2Ray订阅
  toV2raySubscription(nodes) {
    const configs = nodes.map(node => {
      if (node.type === 'vmess') {
        return this.nodeToVmessLink(node);
      } else if (node.type === 'vless') {
        return this.nodeToVlessLink(node);
      }
      return null;
    }).filter(Boolean);

    return btoa(configs.join('\n'));
  }

  // 节点转VMess链接
  nodeToVmessLink(node) {
    const vmessConfig = {
      v: '2',
      ps: node.name,
      add: node.server,
      port: node.port,
      id: node.uuid,
      aid: node.alterId || 0,
      scy: node.cipher || 'auto',
      net: node.network || 'tcp',
      type: node.headerType || 'none',
      host: node.host || '',
      path: node.path || '',
      tls: node.tls ? 'tls' : 'none'
    };

    // 添加 SNI 配置
    if (node.sni) {
      vmessConfig.sni = node.sni;
    }

    const base64Config = btoa(JSON.stringify(vmessConfig));
    return `vmess://${base64Config}`;
  }

  // 节点转VLess链接
  nodeToVlessLink(node) {
    const params = new URLSearchParams();
    params.set('encryption', node.encryption || 'none');
    params.set('type', node.network || 'tcp');
    
    // 安全传输配置
    if (node.reality) {
      params.set('security', 'reality');
      if (node.sni) params.set('sni', node.sni);
      if (node.fingerprint) params.set('fp', node.fingerprint);
      if (node.publicKey) params.set('pbk', node.publicKey);
      if (node.shortId) params.set('sid', node.shortId);
    } else    
    // Flow控制
    if (node.flow) params.set('flow', node.flow);
    
    // 传输协议配置
    switch (node.network) {
      case 'ws':
        if (node.path) params.set('path', node.path);
        if (node.host) params.set('host', node.host);
        break;
      case 'grpc':
        if (node.serviceName) params.set('serviceName', node.serviceName);
        if (node.mode) params.set('mode', node.mode);
        break;
      case 'h2':
        if (node.path) params.set('path', node.path);
        if (node.host) params.set('host', node.host);
        break;
    }
    
    return `vless://${node.uuid}@${node.server}:${node.port}?${params.toString()}#${encodeURIComponent(node.name)}`;
  }
}

module.exports = { NodeConverter };
