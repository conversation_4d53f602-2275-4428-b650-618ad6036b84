// Clash 配置模板
export const clashConfigTemplate = {
  // 基础配置
  global: {
    'global-ua': 'clash',
    mode: 'rule',
    'mixed-port': 7890,
    'allow-lan': true,
    'log-level': 'info',
    'external-controller': '0.0.0.0:9090'
  },

  // DNS 配置
  dns: {
    enable: true,
    ipv6: false,
    'listen': '0.0.0.0:1053',
    'enhanced-mode': 'fake-ip',
    'fake-ip-range': '**********/16',
    'use-hosts': true,
    'default-nameserver': [
      '*********',
      '*********',
      '************'
    ],
    nameserver: [
      'https://doh.pub/dns-query',
      'https://dns.alidns.com/dns-query',
      'https://*******/dns-query',
      'https://*******/dns-query'
    ],
    fallback: [
      'https://*******/dns-query',
      'https://*******/dns-query',
      'https://dns.cloudflare.com/dns-query',
      'https://dns.google/dns-query'
    ],
    'fallback-filter': {
      geoip: true,
      'geoip-code': 'CN',
      ipcidr: [
        '240.0.0.0/4',
        '0.0.0.0/32'
      ],
      domain: [
        '+.google.com',
        '+.facebook.com',
        '+.twitter.com',
        '+.youtube.com',
        '+.xn--ngstr-lra8j.com',
        '+.google.cn',
        '+.googleapis.cn',
        '+.googleapis.com',
        '+.gvt1.com'
      ]
    },
    'fake-ip-filter': [
      '*.lan',
      '*.local',
      '*.localhost',
      'localhost.ptlogin2.qq.com',
      'dns.msftncsi.com',
      'www.msftncsi.com',
      'www.msftconnecttest.com',
      '+.srv.nintendo.net',
      '+.stun.playstation.net',
      'xbox.*.microsoft.com',
      '+.xboxlive.com',
      '+.battlenet.com.cn',
      '+.wotgame.cn',
      '+.wggames.cn',
      '+.wowsgame.cn',
      '+.wargaming.net',
      'proxy.golang.org',
      'stun.*.*',
      'stun.*.*.*',
      '+.stun.*.*',
      '+.stun.*.*.*',
      '+.stun.*.*.*.*',
      'heartbeat.belkin.com',
      '*.linksys.com',
      '*.linksyssmartwifi.com',
      '*.router.asus.com',
      'mesu.apple.com',
      'swscan.apple.com',
      'swquery.apple.com',
      'swdownload.apple.com',
      'swcdn.apple.com',
      'swdist.apple.com'
    ]
  },

  // 代理组模板
  proxyGroups: [
    {
      name: '节点选择',
      type: 'select',
      icon: 'https://cdn.jsdelivr.net/gh/Koolson/Qure@master/IconSet/Color/Proxy.png'
    },
    {
      name: '媒体服务',
      type: 'select',
      icon: 'https://cdn.jsdelivr.net/gh/Koolson/Qure@master/IconSet/Color/Netflix.png'
    },
    {
      name: '微软服务',
      type: 'select',
      icon: 'https://cdn.jsdelivr.net/gh/Koolson/Qure@master/IconSet/Color/Microsoft.png'
    },
    {
      name: '苹果服务',
      type: 'select',
      icon: 'https://cdn.jsdelivr.net/gh/Koolson/Qure@master/IconSet/Color/Apple.png'
    },
    {
      name: 'CDN服务',
      type: 'select',
      icon: 'https://cdn.jsdelivr.net/gh/Koolson/Qure@master/IconSet/Color/OneDrive.png'
    },
    {
      name: 'AI服务',
      type: 'select',
      icon: 'https://cdn.jsdelivr.net/gh/Koolson/Qure@master/IconSet/Color/ChatGPT.png'
    },
    {
      name: 'Telegram',
      type: 'select',
      icon: 'https://cdn.jsdelivr.net/gh/Koolson/Qure@master/IconSet/Color/Telegram.png'
    },
    {
      name: '爱奇艺&哔哩哔哩',
      type: 'select',
      icon: 'https://cdn.jsdelivr.net/gh/Koolson/Qure@master/IconSet/Color/iQIYI&bilibili.png'
    },
    {
      name: 'Steam',
      type: 'select',
      icon: 'https://cdn.jsdelivr.net/gh/Koolson/Qure@master/IconSet/Color/Steam.png'
    },
    {
      name: 'Cloudflare',
      type: 'select',
      icon: 'https://cdn.jsdelivr.net/gh/Koolson/Qure@master/IconSet/Color/Cloudflare.png'
    },
    {
      name: 'OneDrive',
      type: 'select',
      icon: 'https://cdn.jsdelivr.net/gh/Koolson/Qure@master/IconSet/Color/OneDrive.png'
    }
  ],

  // 规则配置
  rules: [
    // 拦截规则
    'RULE-SET,reject_non_ip,REJECT',
    'RULE-SET,reject_domainset,REJECT',
    'RULE-SET,reject_extra_domainset,REJECT',
    'RULE-SET,reject_non_ip_drop,REJECT-DROP',
    'RULE-SET,reject_non_ip_no_drop,REJECT',

    // 域名类规则
    'RULE-SET,telegram_non_ip,Telegram',
    'RULE-SET,apple_cdn,苹果服务',
    'RULE-SET,apple_cn_non_ip,苹果服务',
    'RULE-SET,microsoft_cdn_non_ip,微软服务',
    'RULE-SET,apple_services,苹果服务',
    'RULE-SET,microsoft_non_ip,微软服务',
    'RULE-SET,download_domainset,CDN服务',
    'RULE-SET,download_non_ip,CDN服务',
    'RULE-SET,cdn_domainset,CDN服务',
    'RULE-SET,cdn_non_ip,CDN服务',
    'RULE-SET,stream_non_ip,媒体服务',
    'RULE-SET,ai_non_ip,AI服务',
    'RULE-SET,global_non_ip,节点选择',
    'RULE-SET,domestic_non_ip,DIRECT',
    'RULE-SET,direct_non_ip,DIRECT',
    'RULE-SET,lan_non_ip,DIRECT',
    'GEOSITE,CN,DIRECT',

    // IP 类规则
    'RULE-SET,reject_ip,REJECT',
    'RULE-SET,telegram_ip,Telegram',
    'RULE-SET,stream_ip,媒体服务',
    'RULE-SET,lan_ip,DIRECT',
    'RULE-SET,domestic_ip,DIRECT',
    'RULE-SET,china_ip,DIRECT',
    'GEOIP,LAN,DIRECT',
    'GEOIP,CN,DIRECT',

    // 兜底规则
    'MATCH,节点选择'
  ]
};
