// Clash 配置模板
export const clashConfigTemplate = {
  // 基础配置
  global: {
    'global-ua': 'clash',
    mode: 'rule',
    'mixed-port': 7890,
    'allow-lan': true,
    'log-level': 'info',
    'external-controller': '0.0.0.0:9090'
  },

  // DNS 配置
  dns: {
    enable: true,
    ipv6: false,
    'listen': '0.0.0.0:1053',
    'enhanced-mode': 'fake-ip',
    'fake-ip-range': '**********/16',
    'use-hosts': true,
    'default-nameserver': [
      '*********',
      '*********',
      '************'
    ],
    nameserver: [
      'https://doh.pub/dns-query',
      'https://dns.alidns.com/dns-query',
      'https://*******/dns-query',
      'https://*******/dns-query'
    ],
    fallback: [
      'https://*******/dns-query',
      'https://*******/dns-query',
      'https://dns.cloudflare.com/dns-query',
      'https://dns.google/dns-query'
    ],
    'fallback-filter': {
      geoip: true,
      'geoip-code': 'CN',
      ipcidr: [
        '240.0.0.0/4',
        '0.0.0.0/32'
      ],
      domain: [
        '+.google.com',
        '+.facebook.com',
        '+.twitter.com',
        '+.youtube.com',
        '+.xn--ngstr-lra8j.com',
        '+.google.cn',
        '+.googleapis.cn',
        '+.googleapis.com',
        '+.gvt1.com'
      ]
    },
    'fake-ip-filter': [
      '*.lan',
      '*.local',
      '*.localhost',
      'localhost.ptlogin2.qq.com',
      'dns.msftncsi.com',
      'www.msftncsi.com',
      'www.msftconnecttest.com',
      '+.srv.nintendo.net',
      '+.stun.playstation.net',
      'xbox.*.microsoft.com',
      '+.xboxlive.com',
      '+.battlenet.com.cn',
      '+.wotgame.cn',
      '+.wggames.cn',
      '+.wowsgame.cn',
      '+.wargaming.net',
      'proxy.golang.org',
      'stun.*.*',
      'stun.*.*.*',
      '+.stun.*.*',
      '+.stun.*.*.*',
      '+.stun.*.*.*.*',
      'heartbeat.belkin.com',
      '*.linksys.com',
      '*.linksyssmartwifi.com',
      '*.router.asus.com',
      'mesu.apple.com',
      'swscan.apple.com',
      'swquery.apple.com',
      'swdownload.apple.com',
      'swcdn.apple.com',
      'swdist.apple.com'
    ]
  },

  // 代理组模板
  proxyGroups: [
    {
      name: '节点选择',
      type: 'select',
      icon: 'https://cdn.jsdelivr.net/gh/Koolson/Qure@master/IconSet/Color/Proxy.png'
    },
    {
      name: '媒体服务',
      type: 'select',
      icon: 'https://cdn.jsdelivr.net/gh/Koolson/Qure@master/IconSet/Color/Netflix.png'
    },
    {
      name: '微软服务',
      type: 'select',
      icon: 'https://cdn.jsdelivr.net/gh/Koolson/Qure@master/IconSet/Color/Microsoft.png'
    },
    {
      name: '苹果服务',
      type: 'select',
      icon: 'https://cdn.jsdelivr.net/gh/Koolson/Qure@master/IconSet/Color/Apple.png'
    },
    {
      name: 'CDN服务',
      type: 'select',
      icon: 'https://cdn.jsdelivr.net/gh/Koolson/Qure@master/IconSet/Color/OneDrive.png'
    },
    {
      name: 'AI服务',
      type: 'select',
      icon: 'https://cdn.jsdelivr.net/gh/Koolson/Qure@master/IconSet/Color/ChatGPT.png'
    },
    {
      name: 'Telegram',
      type: 'select',
      icon: 'https://cdn.jsdelivr.net/gh/Koolson/Qure@master/IconSet/Color/Telegram.png'
    },
    {
      name: '爱奇艺&哔哩哔哩',
      type: 'select',
      icon: 'https://cdn.jsdelivr.net/gh/Koolson/Qure@master/IconSet/Color/iQIYI&bilibili.png'
    },
    {
      name: 'Steam',
      type: 'select',
      icon: 'https://cdn.jsdelivr.net/gh/Koolson/Qure@master/IconSet/Color/Steam.png'
    },
    {
      name: 'Cloudflare',
      type: 'select',
      icon: 'https://cdn.jsdelivr.net/gh/Koolson/Qure@master/IconSet/Color/Cloudflare.png'
    },
    {
      name: 'OneDrive',
      type: 'select',
      icon: 'https://cdn.jsdelivr.net/gh/Koolson/Qure@master/IconSet/Color/OneDrive.png'
    }
  ],

  // 规则配置
  rules: [
    // 拦截规则
    'RULE-SET,reject_non_ip,REJECT',
    'RULE-SET,reject_domainset,REJECT',
    'RULE-SET,reject_extra_domainset,REJECT',
    'RULE-SET,reject_non_ip_drop,REJECT-DROP',
    'RULE-SET,reject_non_ip_no_drop,REJECT',

    // 域名类规则
    'RULE-SET,telegram_non_ip,Telegram',
    'RULE-SET,apple_cdn,苹果服务',
    'RULE-SET,apple_cn_non_ip,苹果服务',
    'RULE-SET,microsoft_cdn_non_ip,微软服务',
    'RULE-SET,apple_services,苹果服务',
    'RULE-SET,microsoft_non_ip,微软服务',
    'RULE-SET,download_domainset,CDN服务',
    'RULE-SET,download_non_ip,CDN服务',
    'RULE-SET,cdn_domainset,CDN服务',
    'RULE-SET,cdn_non_ip,CDN服务',
    'RULE-SET,stream_non_ip,媒体服务',
    'RULE-SET,ai_non_ip,AI服务',
    'RULE-SET,global_non_ip,节点选择',
    'RULE-SET,domestic_non_ip,DIRECT',
    'RULE-SET,direct_non_ip,DIRECT',
    'RULE-SET,lan_non_ip,DIRECT',
    'GEOSITE,CN,DIRECT',

    // IP 类规则
    'RULE-SET,reject_ip,REJECT',
    'RULE-SET,telegram_ip,Telegram',
    'RULE-SET,stream_ip,媒体服务',
    'RULE-SET,lan_ip,DIRECT',
    'RULE-SET,domestic_ip,DIRECT',
    'RULE-SET,china_ip,DIRECT',
    'GEOIP,LAN,DIRECT',
    'GEOIP,CN,DIRECT',

    // 兜底规则
    'MATCH,节点选择'
  ],

  // 规则提供者配置
  ruleProviders: {
    reject_non_ip_no_drop: {
      type: 'http',
      behavior: 'classical',
      interval: 43200,
      format: 'text',
      proxy: '节点选择',
      url: 'https://ruleset.skk.moe/Clash/non_ip/reject-no-drop.txt',
      path: './rule_set/sukkaw_ruleset/reject_non_ip_no_drop.txt'
    },
    reject_non_ip_drop: {
      type: 'http',
      behavior: 'classical',
      interval: 43200,
      format: 'text',
      proxy: '节点选择',
      url: 'https://ruleset.skk.moe/Clash/non_ip/reject-drop.txt',
      path: './rule_set/sukkaw_ruleset/reject_non_ip_drop.txt'
    },
    reject_non_ip: {
      type: 'http',
      behavior: 'classical',
      interval: 43200,
      format: 'text',
      proxy: '节点选择',
      url: 'https://ruleset.skk.moe/Clash/non_ip/reject.txt',
      path: './rule_set/sukkaw_ruleset/reject_non_ip.txt'
    },
    reject_domainset: {
      type: 'http',
      behavior: 'domain',
      interval: 43200,
      format: 'text',
      proxy: '节点选择',
      url: 'https://ruleset.skk.moe/Clash/domainset/reject.txt',
      path: './rule_set/sukkaw_ruleset/reject_domainset.txt'
    },
    reject_extra_domainset: {
      type: 'http',
      behavior: 'domain',
      interval: 43200,
      format: 'text',
      proxy: '节点选择',
      url: 'https://ruleset.skk.moe/Clash/domainset/reject_extra.txt',
      path: './sukkaw_ruleset/reject_domainset_extra.txt'
    },
    reject_ip: {
      type: 'http',
      behavior: 'classical',
      interval: 43200,
      format: 'text',
      proxy: '节点选择',
      url: 'https://ruleset.skk.moe/Clash/ip/reject.txt',
      path: './rule_set/sukkaw_ruleset/reject_ip.txt'
    },
    cdn_domainset: {
      type: 'http',
      behavior: 'domain',
      interval: 43200,
      format: 'text',
      proxy: '节点选择',
      url: 'https://ruleset.skk.moe/Clash/domainset/cdn.txt',
      path: './rule_set/sukkaw_ruleset/cdn_domainset.txt'
    },
    cdn_non_ip: {
      type: 'http',
      behavior: 'domain',
      interval: 43200,
      format: 'text',
      proxy: '节点选择',
      url: 'https://ruleset.skk.moe/Clash/non_ip/cdn.txt',
      path: './rule_set/sukkaw_ruleset/cdn_non_ip.txt'
    },
    stream_non_ip: {
      type: 'http',
      behavior: 'classical',
      interval: 43200,
      format: 'text',
      proxy: '节点选择',
      url: 'https://ruleset.skk.moe/Clash/non_ip/stream.txt',
      path: './rule_set/sukkaw_ruleset/stream_non_ip.txt'
    },
    stream_ip: {
      type: 'http',
      behavior: 'classical',
      interval: 43200,
      format: 'text',
      proxy: '节点选择',
      url: 'https://ruleset.skk.moe/Clash/ip/stream.txt',
      path: './rule_set/sukkaw_ruleset/stream_ip.txt'
    },
    ai_non_ip: {
      type: 'http',
      behavior: 'classical',
      interval: 43200,
      format: 'text',
      proxy: '节点选择',
      url: 'https://ruleset.skk.moe/Clash/non_ip/ai.txt',
      path: './rule_set/sukkaw_ruleset/ai_non_ip.txt'
    },
    telegram_non_ip: {
      type: 'http',
      behavior: 'classical',
      interval: 43200,
      format: 'text',
      proxy: '节点选择',
      url: 'https://ruleset.skk.moe/Clash/non_ip/telegram.txt',
      path: './rule_set/sukkaw_ruleset/telegram_non_ip.txt'
    },
    telegram_ip: {
      type: 'http',
      behavior: 'classical',
      interval: 43200,
      format: 'text',
      proxy: '节点选择',
      url: 'https://ruleset.skk.moe/Clash/ip/telegram.txt',
      path: './rule_set/sukkaw_ruleset/telegram_ip.txt'
    },
    apple_cdn: {
      type: 'http',
      behavior: 'domain',
      interval: 43200,
      format: 'text',
      proxy: '节点选择',
      url: 'https://ruleset.skk.moe/Clash/domainset/apple_cdn.txt',
      path: './rule_set/sukkaw_ruleset/apple_cdn.txt'
    },
    apple_services: {
      type: 'http',
      behavior: 'classical',
      interval: 43200,
      format: 'text',
      proxy: '节点选择',
      url: 'https://ruleset.skk.moe/Clash/non_ip/apple_services.txt',
      path: './rule_set/sukkaw_ruleset/apple_services.txt'
    },
    apple_cn_non_ip: {
      type: 'http',
      behavior: 'classical',
      interval: 43200,
      format: 'text',
      proxy: '节点选择',
      url: 'https://ruleset.skk.moe/Clash/non_ip/apple_cn.txt',
      path: './rule_set/sukkaw_ruleset/apple_cn_non_ip.txt'
    },
    microsoft_cdn_non_ip: {
      type: 'http',
      behavior: 'classical',
      interval: 43200,
      format: 'text',
      proxy: '节点选择',
      url: 'https://ruleset.skk.moe/Clash/non_ip/microsoft_cdn.txt',
      path: './rule_set/sukkaw_ruleset/microsoft_cdn_non_ip.txt'
    },
    microsoft_non_ip: {
      type: 'http',
      behavior: 'classical',
      interval: 43200,
      format: 'text',
      proxy: '节点选择',
      url: 'https://ruleset.skk.moe/Clash/non_ip/microsoft.txt',
      path: './rule_set/sukkaw_ruleset/microsoft_non_ip.txt'
    },
    download_domainset: {
      type: 'http',
      behavior: 'domain',
      interval: 43200,
      format: 'text',
      proxy: '节点选择',
      url: 'https://ruleset.skk.moe/Clash/domainset/download.txt',
      path: './rule_set/sukkaw_ruleset/download_domainset.txt'
    },
    download_non_ip: {
      type: 'http',
      behavior: 'domain',
      interval: 43200,
      format: 'text',
      proxy: '节点选择',
      url: 'https://ruleset.skk.moe/Clash/non_ip/download.txt',
      path: './rule_set/sukkaw_ruleset/download_non_ip.txt'
    },
    lan_non_ip: {
      type: 'http',
      behavior: 'classical',
      interval: 43200,
      format: 'text',
      proxy: '节点选择',
      url: 'https://ruleset.skk.moe/Clash/non_ip/lan.txt',
      path: './rule_set/sukkaw_ruleset/lan_non_ip.txt'
    },
    lan_ip: {
      type: 'http',
      behavior: 'classical',
      interval: 43200,
      format: 'text',
      proxy: '节点选择',
      url: 'https://ruleset.skk.moe/Clash/ip/lan.txt',
      path: './rule_set/sukkaw_ruleset/lan_ip.txt'
    },
    domestic_non_ip: {
      type: 'http',
      behavior: 'classical',
      interval: 43200,
      format: 'text',
      proxy: '节点选择',
      url: 'https://ruleset.skk.moe/Clash/non_ip/domestic.txt',
      path: './rule_set/sukkaw_ruleset/domestic_non_ip.txt'
    },
    direct_non_ip: {
      type: 'http',
      behavior: 'classical',
      interval: 43200,
      format: 'text',
      proxy: '节点选择',
      url: 'https://ruleset.skk.moe/Clash/non_ip/direct.txt',
      path: './rule_set/sukkaw_ruleset/direct_non_ip.txt'
    },
    global_non_ip: {
      type: 'http',
      behavior: 'classical',
      interval: 43200,
      format: 'text',
      proxy: '节点选择',
      url: 'https://ruleset.skk.moe/Clash/non_ip/global.txt',
      path: './rule_set/sukkaw_ruleset/global_non_ip.txt'
    },
    domestic_ip: {
      type: 'http',
      behavior: 'classical',
      interval: 43200,
      format: 'text',
      proxy: '节点选择',
      url: 'https://ruleset.skk.moe/Clash/ip/domestic.txt',
      path: './rule_set/sukkaw_ruleset/domestic_ip.txt'
    },
    china_ip: {
      type: 'http',
      behavior: 'ipcidr',
      interval: 43200,
      format: 'text',
      proxy: '节点选择',
      url: 'https://ruleset.skk.moe/Clash/ip/china_ip.txt',
      path: './rule_set/sukkaw_ruleset/china_ip.txt'
    }
  }
};

// 代理组配置策略
export const proxyGroupStrategies = {
  '节点选择': (proxyNames) => ['DIRECT'].concat(proxyNames),
  '媒体服务': (proxyNames) => ['节点选择', 'DIRECT'].concat(proxyNames),
  '微软服务': (proxyNames) => ['节点选择', 'DIRECT'].concat(proxyNames),
  '苹果服务': (proxyNames) => ['节点选择', 'DIRECT'].concat(proxyNames),
  'CDN服务': (proxyNames) => ['节点选择', 'DIRECT'].concat(proxyNames),
  'AI服务': (proxyNames) => ['节点选择', 'DIRECT'].concat(proxyNames),
  'Telegram': (proxyNames) => ['节点选择', 'DIRECT'].concat(proxyNames),
  '爱奇艺&哔哩哔哩': (proxyNames) => ['DIRECT', '节点选择'].concat(proxyNames),
  'Steam': (proxyNames) => ['DIRECT', '节点选择'].concat(proxyNames),
  'Cloudflare': (proxyNames) => ['节点选择', 'DIRECT'].concat(proxyNames),
  'OneDrive': (proxyNames) => ['节点选择', 'DIRECT'].concat(proxyNames)
};

// 构建完整的 Clash 配置
export function buildClashConfig(proxies, options = {}) {
  const proxyNames = proxies.map(p => p.name);

  // 构建代理组
  const proxyGroups = clashConfigTemplate.proxyGroups.map(group => ({
    ...group,
    proxies: proxyGroupStrategies[group.name]
      ? proxyGroupStrategies[group.name](proxyNames)
      : ['节点选择', 'DIRECT'].concat(proxyNames)
  }));

  return {
    // 基础配置
    ...clashConfigTemplate.global,

    // DNS 配置
    dns: clashConfigTemplate.dns,

    // 代理节点
    proxies: proxies,

    // 代理组
    'proxy-groups': proxyGroups,

    // 规则
    rules: clashConfigTemplate.rules,

    // 规则提供者
    'rule-providers': clashConfigTemplate.ruleProviders
  };
}
